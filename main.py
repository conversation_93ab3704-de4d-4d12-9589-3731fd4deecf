import pyautogui as py
import time 


class PPokerBot:
    def __init__(self, client_id: str, clube: str, fichas_method: str, fichas_amount: float,):
        self.client_id = client_id
        self.fichas_method = fichas_method
        self.fichas_amount = fichas_amount
        self.clube = clube
        self.clube_map = {
            'threehand':'cluble_threehand.PNG',
            'fourhand': 'cluble_fourhand.PNG',
    }

    def wait_for_image(self, image_path: str, max_timeout: int = 20, confidence: int = 0.9):
        while max_timeout > 0:
            try:
                location = py.locateOnScreen(image_path, confidence=confidence)
                if location is not None:
                    return location
            except Exception as e:
                print(f"[!] Unexpected error while searching for image: {image_path}")
            max_timeout -= 1
            time.sleep(1)
        raise Exception(f"Image not found: {image_path}")
    
    def check_aviso(self, image_path: str, max_timeout: int = 5):
        while max_timeout > 0:
            try:
                location = py.locateOnScreen(image_path, confidence=0.9)
                if location is not None:
                    return location
            except Exception as e:
                print("Esperando aviso...")
            max_timeout -= 1
            time.sleep(1)
        return None

    def run(self):
        # Tenta localizar a imagem na tela
        
        clube_location = self.wait_for_image(self.clube_map[self.clube])
        self.click_on_image(clube_location)
        time.sleep(3)


        aviso_location = self.check_aviso('aviso_after_clube.PNG')
        if aviso_location:
            self.click_on_image(aviso_location)
            time.sleep(3)

        contador_location = self.wait_for_image('contador_teste.PNG', confidence=0.97)
        self.click_on_image(contador_location)
        time.sleep(3)

        # Isso é para ter certeza que carregou
        fichas_carregadas = self.check_aviso('fichas_carregadas.PNG', max_timeout=15)
        if not fichas_carregadas:
            raise Exception("Ppoker não carregou após clicar no contador")

        pesquisar_input_location = self.wait_for_image('pesquisar_input.PNG', confidence=0.8)
        self.click_on_image(pesquisar_input_location)
        time.sleep(5)
        py.write(self.client_id)
        time.sleep(3)

        pesquisar_click_location = self.wait_for_image('pesquisar_click.PNG', confidence=0.8)
        self.click_on_image(pesquisar_click_location)
        time.sleep(3)

        select_user_location = self.wait_for_image('selecionar_user.PNG', confidence=0.8)
        time.sleep(3)
        self.click_on_image(select_user_location)
        time.sleep(3)

        if self.fichas_method == 'enviar':
            file_path = 'enviar_fichas.PNG'
        else:
            file_path = 'devolver_fichas.PNG'

        fichas_location = self.wait_for_image(file_path, confidence=0.86)
        self.click_on_image(fichas_location)
        time.sleep(3)

        py.write(str(self.fichas_amount))
        time.sleep(2)

        confirmar_location = self.wait_for_image('confirmar.PNG', confidence=0.8)
        self.click_on_image(confirmar_location)
        time.sleep(3)
        
        fechar_location = self.wait_for_image('fechar_after_send.PNG', confidence=0.87)
        self.click_on_image(fechar_location)
        time.sleep(3)

        voltar_location = self.wait_for_image('voltar_button.PNG', confidence=0.8)
        self.click_on_image(voltar_location)
        time.sleep(3)

        print('[+] Fichas enviadas com sucesso!')

    def click_on_image(self, location):
        center_point = py.center(location)
        py.moveTo(center_point.x, center_point.y, duration=0.5)
        py.click()
    

if __name__ == "__main__":
    time.sleep(10)
    user_input = {
        "client_id": "11857361",
        "clube": "fourhand",
        "fichas_method": "enviar",
        "fichas_amount": 1
    }
    bot = PPokerBot(**user_input)
    bot.run()