from fastapi import FastAPI
from pydantic import ValidationError
from schema import PpokerInput
from main import PPokerBot
import uvicorn
import time

app = FastAPI(
    title="PPoker Automation API",
    description="Automatiza interações com o aplicativo PPoker",
    version="1.0.0"
)

@app.get("/", summary="Verifica se a API está online")
def read_root():
    return {"message": "PPoker Automation API online"}

@app.post("/ppoker", summary="Executa automação com os parâmetros fornecidos")
def call_automation(automation_input: PpokerInput):
    try:
        time.sleep(10)
        bot = PPokerBot(**automation_input.model_dump())
        bot.run()
        return {"status": "success"}
    except ValidationError as e:
        return {"status": "error", "details": e.errors()}
    except Exception as e:
        return {"status": "error", "details": str(e)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5000)
